<template>
  <div class="marker-box">
    <div id="image-browser">
      <div id="image-broswer-main"></div>
      <div id="image-browser-navigator"></div>
    </div>

    <div class="tools-box">
      <div>
        <div class="tools-item" :class="{ active: item.id === activeId }" :key="item.id" @click="toolClick(item)"
          v-for="item in tools">
          {{ item.label }}
        </div>
      </div>
      <div class="tools-other">
        <input type="text" placeholder="输入stroke" v-model="strokeColor" style="width: 100px" />
        <input type="text" placeholder="输入stroke宽度" v-model.number="strokeWidth" style="width: 100px" />
      </div>
      <div>
        <button @click="flipViewer">镜像</button>
        <button @click="toggleKonva">全隐/显</button>
      </div>
    </div>

    <!-- 历史列表 -->
    <div class="history-box">
      <div style="display: flex; justify-content: center; width: 100%">历史记录</div>
      <div style="display: flex; justify-content: space-between; width: 100%">
        <button style="cursor: pointer" @click="toPrev" :disbaled="historyIndex <= 0">
          上一步
        </button>
        <button style="cursor: pointer" @click="toNext" :disbaled="historyIndex >= historys.length - 1">
          下一步
        </button>
      </div>
      <div class="history-item" v-for="(item, index) in historys" :key="index">
        <!-- <span>{{ item }}</span> -->
        <span>{{ index + 1 }}</span>
        <!-- <span>id: {{ item.id }}</span>
        <span>x: {{ item.attrs.x }}</span>
        <span>y: {{ item.attrs.x }}</span> -->
      </div>
    </div>

    <!-- 标注列表 -->
    <div class="labels-box">
      <div class="labels-item" v-for="(item, index) in labels"
        :style="{ backgroundColor: item.attrs?.active ? 'red' : '' }">
        <span>序号: {{ index + 1 }}</span>
        <span>{{ item.Name }}</span>
        <span class="tool" @click="handleChange(item)">变</span>
        <span class="tool" @click="handlePositing(item)">定位</span>
        <span class="tool" @click="handleEdit(item)">编辑</span>
        <span class="tool" @click="handleToggle(item)">隐/显</span>
        <span class="tool" @click="handleRemove(item)">删除</span>
        <span class="tool" @click="handleColor(item)">颜色</span>
        <span class="tool" @click="handleShowGroup(item)">组显</span>
        <span class="tool" @click="handleHideGroup(item)">组隐</span>
        <span class="tool" @click="handleShowAll(item)">全显示</span>
        <span class="tool" @click="handleHideAll(item)">全隐</span>
        <span class="tool" @click="hadnleDes(item)">批量删除</span>
      </div>
    </div>

    <!-- <AI v-if="viewer" :viewer="viewer" :flip="flip"></AI> -->
    <ScreenShot v-if="viewer" :viewer="viewer"></ScreenShot>
  </div>
</template>

<script lang="ts" setup>
interface ToolItem {
  label: string;
  id: number;
  value: string;
}

import { onMounted, ref, watch } from 'vue';
import { data as testLabel } from '../../../public/LabelData/test1';
import OpenSeadragon from 'openseadragon';
import SQMarker from './components/sqMarker';
import AI from './components/pixi/index.vue';
import ScreenShot from '@/components/screenShot/index.vue';
import { getRandomHexColor } from '../../utils/utils';
import { localJSONToRadingSoftwareJSON, RadingSoftwareJSONTolocalJSON } from '../../utils/JSON';
const viewer = ref<OpenSeadragon.Viewer | null>(null);
const sqMarker = ref<SQMarker | null>(null);
const tools = ref<ToolItem[]>([
  { label: '直线√', id: 1, value: 'line' },
  { label: '箭头√', id: 2, value: 'arrow' },
  { label: '矩形√', id: 3, value: 'rect' },
  { label: '圆形√', id: 4, value: 'circle' },
  { label: '椭圆√', id: 5, value: 'ellipse' },
  { label: '夹角-待优', id: 6, value: 'angle' },
  { label: '同心圆-待优', id: 7, value: 'ring' },
  { label: '标旗', id: 8, value: 'flag' },
  { label: '折线√', id: 9, value: 'foldLine' },
  { label: '多边形√', id: 10, value: 'polygon' },
  { label: '闭合曲线√', id: 11, value: 'closedCurve' },
  { label: '自由曲线√', id: 12, value: 'freeCurve' },
  { label: '尺子√', id: 13, value: 'ruler' },
  { label: '魔杖', id: 14, value: 'wand' },
]);
const strokeColor = ref('red');
const strokeWidth = ref(1);
const labels = ref<any[]>([]);
const historys = ref<any[]>([]);
const activeId = ref(-1);
const toolClick = (item: ToolItem) => {
  activeId.value = item.id;
  if (item.value === 'line') {
    sqMarker.value?.drawLine({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
      groupId: 10086,
    });
  }
  if (item.value === 'rect') {
    sqMarker.value?.drawRect({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
      group: '默认',
    });
  }
  if (item.value === 'circle') {
    sqMarker.value?.drawCircle({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
      group: '默认2',
      parentGroup: '默认',
    });
  }
  if (item.value === 'ellipse') {
    sqMarker.value?.drawEllipse({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
      group: '3',
      parentGroup: '2',
    });
  }
  if (item.value === 'arrow') {
    sqMarker.value?.drawArrow({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
      group: '4',
      parentGroup: '3',
    });
  }
  if (item.value === 'flag') {
    sqMarker.value?.drawFlag({
      group: 'isFlag',
      parentGroup: '默认',
    });
  }
  if (item.value === 'angle') {
    sqMarker.value?.drawAngle({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
  if (item.value === 'foldLine') {
    sqMarker.value?.drawFoldLine({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
  if (item.value === 'ring') {
    sqMarker.value?.drawRing({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
      group: 'ring',
      parentGroup: '默认',
    });
  }
  if (item.value === 'polygon') {
    sqMarker.value?.drawPolygon({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
  if (item.value === 'closedCurve') {
    sqMarker.value?.drawClosedCurve({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
  if (item.value === 'freeCurve') {
    sqMarker.value?.drawFreeCurve({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
  if (item.value === 'ruler') {
    sqMarker.value?.drawRuler({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
  if (item.value === 'wand') {
    sqMarker.value?.drawWand({
      baseBufferSize: 100,
      baseSensitivity: 10,
      mode: 'rgb'
    });
  }
};

const initOsd = () => {
  const tileSource: any = {
    Image: {
      xmlns: 'http://schemas.microsoft.com/deepzoom/2008',
      Url: 'http://openseadragon.github.io/example-images/highsmith/highsmith_files/',
      Format: 'jpg',
      Overlap: '2',
      TileSize: '256',
      Size: {
        Height: '9221',
        Width: '7026',
      },
    },
  };

  viewer.value = new OpenSeadragon.Viewer({
    // id: "image-browser-navigator",
    prefixUrl: 'openseadragon/images/',
    showNavigationControl: false,
    autoResize: true,
    preserveImageSizeOnResize: true,
    tileSources: [tileSource],

    id: 'image-broswer-main',
    // prefixUrl: '/openseadragon/',
    navigatorId: 'image-browser-navigator',
    navigatorAutoResize: true, //导航器大小自动调整
    // navigatorBackground: "#fff", // 导航背景颜色
    showNavigator: true, //显示导航图
    placeholderFillStyle: '#f3f3f3', //初始化时，图片未载入现实的样式，默认是白色
    debugGridColor: ['#000000'],
    minZoomImageRatio: 0.3,
    maxZoomPixelRatio: 5,
    zoomPerScroll: 1.3, //滚轮放大倍率
    navigatorOpacity: 1, //导航图透明度
    // showNavigationControl: false,
    gestureSettingsMouse: {
      clickToZoom: false,
    },
    iOSDevice: true,
    constrainDuringPan: true,
    animationTime: 1,
    crossOriginPolicy: 'Anonymous', // 异步问题

    // navigatorId: "image-browser-navigator",
    // navigatorAutoResize: true, //导航器大小自动调整
    navigatorBackground: 'red',
    // showNavigator: true, //显示导航图
    // placeholderFillStyle: "#f3f3f3", //初始化时，图片未载入现实的样式，默认是白色
    // debugGridColor: ["pink"],
    // minZoomImageRatio: 0.5,
    // maxZoomPixelRatio: 1,
    smoothTileEdgesMinZoom: Infinity,
    // zoomPerScroll: 1.3, //滚轮放大倍率
    maxImageCacheCount: 100,
    // navigatorPosition: "TOP_LEFT",
    // navigatorLeft: 0,
    // navigatorTop: 0,
    // navigatorHeight: 200,
    // navigatorWidth: 200,
    // navigatorOpacity: 1, //导航图透明度
    showZoomControl: false, //不显示放大按钮
    showHomeControl: false, //显示主页按钮
    showFullPageControl: false, //显示全屏按钮
    // gestureSettingsMouse: {
    // clickToZoom: false,
    // },
    // preserveImageSizeOnResize: true,
    // animationTime: 1,
    // showReferenceStrip:true,
    visibilityRatio: 0.5,
    // collectionMode: true,
    // defaultZoomLevel:0.01
    // crossOriginPolicy: "Anonymous", // 图片异步问题
    gestureSettingsTouch: {
      dblClickToZoom: false,
    },
  });
};

const flip = ref(false);
const flipViewer = () => {
  flip.value = !flip.value;
  viewer.value?.viewport.setFlip(flip.value);
  const konvaOverlay = document.getElementById('konva-overlay');
  if (konvaOverlay) {
    konvaOverlay.style.transform = flip.value ? 'scaleX(-1)' : '';
  }

  // setFilp
};
const handleChange = (item: any) => {
  sqMarker.value?.editor.setAttr(item.id, {
    stroke: '#060606',
    strokeWidth: '10',
  });
};
const handlePositing = (item: any) => {
  sqMarker.value?.editor.positioningMark(item.id);
};

const handleEdit = (item: any) => {
  sqMarker.value?.editor.editMark(item.id);
};

const handleToggle = (item: any) => {
  sqMarker.value?.editor.toggleMark(item.id);
};

const handleRemove = (item: any) => {
  sqMarker.value?.editor.destroyMark(item.id);
  sqMarker.value?.label.delLabel(item);
};

const handleColor = (item: any) => {
  sqMarker.value?.editor.setAttr(item.id, {
    stroke: getRandomHexColor(),
  });
};

const handleDrawEnd = (node: any) => {
  console.log('drawing end');
  sqMarker.value?.editor.setLabelsByLayer();
};

const toPrev = () => {
  sqMarker?.value?.editor?.drawer?.prevHistory();
};
const toNext = () => {
  sqMarker?.value?.editor?.drawer?.nextHistory();
};

const handleShowGroup = (item: any) => {
  sqMarker?.value?.editor?.showGroup('groupId', 10086);
};

const handleHideGroup = (item: any) => {
  sqMarker?.value?.editor?.hideGroup('groupId', 10086);
};

const handleShowAll = (item: any) => {
  sqMarker?.value?.editor?.showAllItems();
};

const handleHideAll = (item: any) => {
  sqMarker?.value?.editor?.hideAllItems();
};

const hadnleDes = (item: any) => {
  sqMarker?.value?.editor?.destroyMarkers((el) => {
    return el.attrs.group === item.attrs.group;
  });
};

const toggleKonva = () => {
  sqMarker.value?.hideKonva();
};

const fetchJsonData = () => {
  fetch('./jsonData/baseJSON/local.json')
    .then((response) => {
      return response.json();
    })
    .then((data) => {
      // console.log(data, 'fetchJsonData');
      localJSONToRadingSoftwareJSON(data);
    });
};
const fetchReadingJsonData = () => {
  fetch('./jsonData/baseJSON/reading.json')
    .then((response) => {
      return response.json();
    })
    .then((data) => {
      if (viewer.value) {
        const result = RadingSoftwareJSONTolocalJSON(viewer.value, data);
        console.log(result, 'RadingSoftwareJSONTolocalJSON');
        sqMarker.value?.editor?.drawer?.addMarker(result);
      }
    });
};

const historyIndex = ref(-1);
onMounted(async () => {
  initOsd();
  if (viewer.value) {
    sqMarker.value = new SQMarker(viewer.value, { needCountArea: true });
    sqMarker.value?.editor?.drawer?.setDrawEndCallback(handleDrawEnd);
    sqMarker.value.label.attach({
      update: (result?: any) => {
        labels.value = result;
        console.log('updateupdateupdateupdate', result);
        console.log(localJSONToRadingSoftwareJSON(result), 'localJSONToRadingSoftwareJSON(result)');
        // console.log('updateupdateupdateupdate', JSON.stringify(result));
        sqMarker?.value?.editor?.drawer?.on?.('history-change', ({ records, index }) => {
          // console.log('history-change', records);
          historys.value = records.map((e: any) => {
            return {
              ...e,
              // id: e.getId(),
            };
          });
          // historyIndex.value = index
        });
      },
    });
    // sqMarker.value.editor?.drawer?.addMarker(testLabel);
    // sqMarker.value.editor.setLabelsByLayer();
    sqMarker.value?.setRatio({ unit: 'um', scales: 0.3 });
    // fetchJsonData();
    // fetchReadingJsonData();
  }
});

watch(
  () => [strokeColor.value, Number(strokeWidth.value)],
  () => {
    sqMarker.value?.setDrawingOption({
      stroke: strokeColor.value,
      strokeWidth: Number(strokeWidth.value),
    });
  }
);
</script>

<style lang="scss">
#image-browser {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;

  #image-broswer-main {
    height: 100%;
    width: 100%;
  }

  #image-browser-navigator {
    position: absolute !important;
    z-index: 9;
    width: 211px;
    height: 201px;
    top: 19px;
    left: 25px;
    // background: #fff;
    border: 1px solid red;
  }

  @media screen and (max-width: 768px) {
    #image-browser-navigator {
      position: absolute !important;
      z-index: 9;
      width: 100px;
      height: 100px;
      top: 14px;
      left: 14px;
      border: 1px solid red;
    }
  }

  //大于768px
  @media screen and (min-width: 768px) {
    #image-browser-navigator {
      position: absolute !important;
      z-index: 9;
      width: 211px;
      height: 201px;
      top: 19px;
      left: 25px;
      border: 1px solid red;
    }
  }
}

.screen-shot-toolbar {
  width: 120px;
  height: 42px;
  border-radius: 4px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
}

.marker-box {
  width: 100%;
  height: 100%;
  position: relative;
}

.canvas-box {
  width: 100%;
  height: 100%;
  background-color: #ccc;
}

.tools-box {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 2999;
  display: flex;
  flex-wrap: wrap;
  width: 100px;

  .tools-item {
    background-color: #ccc;
    cursor: pointer;
    width: 100px;
    overflow: hidden;
    height: 20px;

    &:hover {
      background-color: red;
    }

    &.active {
      background-color: red;
    }
  }
}

.labels-box {
  position: absolute;
  right: 20px;
  top: 400px;
  z-index: 2999;
  display: flex;
  flex-wrap: wrap;
  width: 400px;

  .labels-item {
    width: 100%;
    background-color: #ccc;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .tool {
      cursor: pointer;

      &:hover {
        background-color: red;
      }
    }
  }
}

.history-box {
  position: absolute;
  right: 200px;
  left: 20px;
  top: 250px;
  z-index: 2999;
  display: flex;
  flex-wrap: wrap;
  width: 300px;

  .history-item {
    width: 100%;
    background-color: #ccc;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .tool {
      cursor: pointer;

      &:hover {
        background-color: red;
      }
    }
  }
}
</style>
