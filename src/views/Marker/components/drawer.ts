/**
 * 绘画器
 */
// import _ from 'lodash';

import { Circle, CircleConfig } from 'konva/lib/shapes/Circle';
import { Shape, ShapeConfig } from 'konva/lib/Shape';
import { Ellipse, EllipseConfig } from 'konva/lib/shapes/Ellipse';
import { Line, LineConfig } from 'konva/lib/shapes/Line';
import { Rect, RectConfig } from 'konva/lib/shapes/Rect';
import Konva from 'konva';
import { Layer } from 'konva/lib/Layer';
import { DrawStatus, DrawType, HistoryRecordTypes } from './editorEnum';
import { Arrow, ArrowConfig } from 'konva/lib/shapes/Arrow';
import { Transformer } from 'konva/lib/shapes/Transformer';
import Label from './label';
import OpenSeadragon, { Point } from 'openseadragon';
import { Group } from 'konva/lib/Group';
import mitt, { type Emitter } from 'mitt';
import { OpenSeadragonRatio } from '@/views/Drawers/Drawer.types';
import { isReadingJson } from '@/utils/index';
import { localJSONToRadingSoftwareJSON, RadingSoftwareJSONTolocalJSON } from '@/utils/JSON';

import {
  LineDrawer,
  RectDrawer,
  EllipseDrawer,
  ArrowDrawer,
  CircleDrawer,
  FoldLineDrawer,
  PolygonDrawer,
  FreeCurveDrawer,
  ClosedCurveDrawer,
  FlagDrawer,
  RulerDrawer,
  AngleDrawer,
  RingDrawer,
  WandDrawer,
} from '@/views/Drawers/index';
export interface DrawerConfig {
  layer: Layer; //画图层
  viewer: OpenSeadragon.Viewer; //画图层
  needCountArea?: boolean; //是否需要面积计算
  Label: Label;
}

export interface DrawPoint {
  x: number;
  y: number;
  imageX?: number;
  imageY?: number;
  pcx?: number;
  pcy?: number;
}

export enum DrawerType {
  RectDrawer,
  LineDrawer,
  EllipseDrawer,
  ArrowDrawer,
  CircleDrawer,
  FoldLineDrawer,
  PolygonDrawer,
  FreeCurveDrawer,
  ClosedCurveDrawer,
  FlagDrawer,
  RulerDrawer,
  AngleDrawer,
  RingDrawer,
}

export class Drawer {
  _drawType!: DrawType | null; //绘画形状类型
  _layer: Layer; //画图层
  // _node!: Rect | Circle | Ellipse | Line | Arrow | null | Group; //绘画元素
  _node!: any;
  _status: DrawStatus = DrawStatus.end; //画图状态
  _transformer: Transformer | null; //用来调整元素的调整器
  _label: Label;
  _viewer: OpenSeadragon.Viewer;
  _drawEndCallback?: Function;
  _selectCallback?: Function;
  _cancelSelect?: Function;
  history: any[] = [{}]; // 用来存放node的历史
  historyIndex = -1;
  private _drawerConfig: DrawerConfig;
  private _ratio?: OpenSeadragonRatio;
  private _graphList: DrawerType[] = [];

  protected emitter: Emitter<any> = mitt();
  on: Emitter<any>['on'];
  off: Emitter<any>['off'];
  emit: Emitter<any>['emit'];
  constructor(config: DrawerConfig) {
    this._layer = config.layer;
    this._viewer = config.viewer;
    this._drawerConfig = config;
    this._label = config.Label;
    this.on = this.emitter.on.bind(this.emitter);
    this.off = this.emitter.off.bind(this.emitter);
    this.emit = this.emitter.emit.bind(this.emitter);
    this.updateHistory();
  }

  //是否有绘画对象
  get hasDrawer(): boolean {
    return this._node ? true : false;
  }

  //绘画状态
  get status(): DrawStatus {
    return this._status;
  }

  //缩放大小
  get scale(): number {
    return this._node ? this._node.scaleX() : 1;
  }
  //缩放大小
  get layerScale(): number {
    return this._layer ? this._layer.scaleX() : 1;
  }

  //绘画元素
  get node(): Rect | Circle | Ellipse | Line | null | Arrow | Group {
    return this._node;
  }

  setStatus(status: DrawStatus) {
    this._status = status;
    return this._status;
  }

  setDrawEndCallback(callback?: Function) {
    if (callback) {
      this._drawEndCallback = callback;
    }
  }

  prevHistory() {
    let record = null;
    // if (this.historyIndex === 0) {
    //   record = this.history[0];
    // } else {
    record = this.history[this.historyIndex - 1];
    // }
    if (record) {
      this.addMarker(record);
      this.historyIndex--;
      // 历史变化事件
      this.emit('history-change', {
        records: this.history,
        index: this.historyIndex,
      });
    }
  }

  nextHistory() {
    const record = this.history[this.historyIndex + 1];
    if (record) {
      this.addMarker(record);
      this.historyIndex++;
      // 历史变化事件
      this.emit('history-change', {
        records: this.history,
        index: this.historyIndex,
      });
    }
  }

  updateHistory(type?: HistoryRecordTypes) {
    this.history.splice(this.historyIndex + 1);
    this.history.push(JSON.parse(JSON.stringify(this._label.labels)));
    this.historyIndex = this.history.length - 1;
    this.emit('history-change', {
      records: this.history,
      index: this.historyIndex,
    });
  }

  // 获取当前鼠标位置
  getCurPoint() {
    const touchPos = this._layer.getStage().getPointerPosition();
    const x = touchPos?.x as number;
    const y = touchPos?.y as number;
    const isFlipped = document
      .getElementById('konva-overlay')
      ?.style.transform.includes('scaleX(-1)');
    let adjustedX = x;
    if (isFlipped) {
      const stageWidth = this._layer.getStage().width();
      adjustedX = stageWidth - x; // 进行水平翻转
    }
    const curPoint = this.konvaPointFromPixl(adjustedX, y);
    return curPoint;
  }

  // 鼠标在浏览器的坐标转为KONVA的坐标点
  konvaPointFromPixl(x: number, y: number) {
    var viewportPoint = this._viewer.viewport.pointFromPixel(new Point(x, y));
    var imagePoint = this._viewer.viewport.viewportToImageCoordinates(viewportPoint);

    // 左上坐标
    var pixelNoRotate = this._viewer.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(0, 0),
      true
    );
    const point = this._viewer.viewport.pointFromPixel(new Point(x, y));
    var pixel = this._viewer.viewport.pixelFromPointNoRotate(point); //当前坐标转化为旋转度数为0度的实际坐标
    return {
      x: (pixel.x - pixelNoRotate.x) / this.layerScale,
      y: (pixel.y - pixelNoRotate.y) / this.layerScale,
      imageX: imagePoint.x,
      imageY: imagePoint.y,
    };
  }

  // 开始绘画
  start() {
    this._status = DrawStatus.drawing;
  }

  // 结束绘画
  end() {
    this._status = DrawStatus.end;
    if (this._node) {
      this._node.setAttrs({
        dash: [],
      });
      this._drawEndCallback?.(this._node, this._layer);
      this.updateHistory();
    }
    this._node = null;
  }

  selectCallback(cb: Function) {
    this._selectCallback = cb;
  }
  cancelSelectCallback(cb: Function) {
    this._cancelSelect = cb;
  }

  select(node: any) {
    const resizeEnabled = node.attrs.name !== 'flag';
    this.cancelSelect();
    this._status = DrawStatus.select;
    this._transformer = new Konva.Transformer({
      node: node,
      // centeredScaling: true,
      flipEnabled: false,
      ignoreStroke: true,
      resizeEnabled: resizeEnabled,
    });
    // if (node.attrs.name === 'line') {
    //   this._transformer.setAttr('enabledAnchors', ['middle-left', 'middle-right']);
    // }
    this._node = node;
    this._node?.setAttrs({
      draggable: true,
      active: true,
      isSelect: true,
    });
    this._layer.add(this._transformer);
    this._selectCallback?.(node);
    console.log(this._status, '_status_status');
    // this._drawEndCallback?.(this._node, this._layer);
  }

  cancelSelect() {
    if (this._status === DrawStatus.select) {
      if (this._transformer) {
        this._transformer.destroy();
        this._layer.draw();
      }
      this._node?.setAttrs({
        draggable: false,
        active: false,
        isSelect: false,
      });
      this._node = null;
    }
    this._node?.setAttrs({
      draggable: false,
    });
    this._status = DrawStatus.end;
    this._cancelSelect?.();

    // 不能为null的原因是双击的时候node为null就不能摧毁 会导致layer多一个node
    // this._node = null;
    // 要设为null的原因是因为选中之后再点击stage会导致layer多一个node
  }

  _nodeDestroy() {
    if (this._node) {
      this._node.remove();
      this._node.destroy();
      this._layer.draw();
      this._node = null;
    }
    this._node?.nodeDestroy();
  }

  deleteNode() {
    this._transformer?.destroy();
    this._node = null;
    this._status = DrawStatus.end;
  }

  /**
   * 统一处理draw的地方
   *
   */
  GraphDrawerMap = {
    rect: RectDrawer,
    line: LineDrawer,
    ellipse: EllipseDrawer,
    arrow: ArrowDrawer,
    circle: CircleDrawer,
    foldLine: FoldLineDrawer,
    polygon: PolygonDrawer,
    freeCurve: FreeCurveDrawer,
    closedCurve: ClosedCurveDrawer,
    flag: FlagDrawer,
    ruler: RulerDrawer,
    angle: AngleDrawer,
    ring: RingDrawer,
    wand: WandDrawer,
  };
  currentGraph: any;
  drawContextmenutHandler(options: any) {
    this.currentGraph?.drawContextmenu();
    this._status = this.currentGraph?.status;
    console.log(1);
  }
  drawStartHandler(options: any) {
    // 状态为结束或者没值的时候才需要new
    if (!this.currentGraph || this._status === DrawStatus.end) {
      const drawer = this.GraphDrawerMap[options.type as keyof typeof this.GraphDrawerMap];
      if (drawer) {
        // 为 WandDrawer 特殊处理配置参数
        if (options.type === 'wand') {
          this.currentGraph = new drawer(
            this._layer,
            this._viewer,
            {
              ...options,
              needCountArea: this._drawerConfig.needCountArea,
              needResize: false,
            },
            {
              baseBufferSize: options.baseBufferSize,
              baseSensitivity: options.baseSensitivity,
              mode: options.mode,
            }
          );
        } else {
          this.currentGraph = new drawer(this._layer, this._viewer, {
            ...options,
            needCountArea: this._drawerConfig.needCountArea,
            needResize: false,
          });
        }
        this.currentGraph?.setRatio(this._ratio);

        this.currentGraph?.drawDown();
        this._status = this.currentGraph?.status;
        this.currentGraph.on?.('drawingEnd', (result: any) => {
          if (result.node) {
            this._drawEndCallback?.(result.node, this._layer);
            this.currentGraph = null;
          }
        });
        this._graphList.push(this.currentGraph);
      }
    }
  }

  drawMoveHandler(options: any) {
    // console.log(this.currentGraph, 'currentGraphcurrentGraph')
    this.currentGraph?.drawMove();
    this._status = this.currentGraph?.status;
  }

  drawEndHandler(options: any) {
    this.currentGraph?.drawUp(options);
    this._status = this.currentGraph?.status;
  }

  drawDblClick(options: any) {
    this.currentGraph?.drawDblClick(options);
    this._status = this.currentGraph?.status;
  }

  drawZoom(options: any) {
    this.currentGraph?.drawZoom(options);
  }

  addMarkerByLabelsDTO(LabelsDTO: any): void {
    const labels = this.addMarker(LabelsDTO.GroupModel.Labels);
  }
  setRatio(scale: OpenSeadragonRatio) {
    this._ratio = scale;
    this.currentGraph?.setRatio(this._ratio);
  }
  //添加标注
  addMarker(markers: any, needRemoveChildren = true): void {
    let result = [];
    if (isReadingJson(markers)) {
      result = RadingSoftwareJSONTolocalJSON(this._viewer, markers);
    } else {
      result = markers;
    }
    needRemoveChildren && this._layer.removeChildren();
    if (result.length > 0) {
      for (let marker of result) {
        delete marker?.attrs?.image;
        const drawerName = marker.name as keyof typeof this.GraphDrawerMap;
        if (!drawerName) {
          return console.error('name为空');
        }
        let node = null;
        const drawer = this.GraphDrawerMap[drawerName];
        if (drawer) {
          // 为 WandDrawer 特殊处理配置参数
          let drawerInstance;
          if (drawerName === 'wand') {
            drawerInstance = new drawer(
              this._layer,
              this._viewer,
              {
                needCountArea: this._drawerConfig.needCountArea,
              },
              {
                baseBufferSize: marker.wandOptions?.baseBufferSize,
                baseSensitivity: marker.wandOptions?.baseSensitivity,
                mode: marker.wandOptions?.mode,
              }
            );
          } else {
            drawerInstance = new drawer(this._layer, this._viewer, {
              needCountArea: this._drawerConfig.needCountArea,
            });
          }
          drawerInstance.setRatio(this._ratio);
          node = drawerInstance.createNode();
          node?.setAttrs({ ...marker.attrs, draggable: false, isSelect: false, dash: [] });
          node?.id(marker.id);

          // Group
          const childs = marker?.children || [];
          if (childs?.length > 0) {
            (node as Group).find((node: any) => {
              const tName = node.attrs.tName;
              const item = childs.find((child: any) => {
                if (typeof child === 'string') {
                  return JSON.parse(child).attrs.tName === tName;
                } else {
                  return child.attrs.tName === tName;
                }
              });
              console.log(item, 'itemitemitem');
              node.setAttrs(typeof item === 'string' ? JSON.parse(item) : item.attrs);
            });
          }
          this._layer.add(node as Shape<ShapeConfig>);
        }
      }
    }
  }
}
