import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';

// 区域接口
interface Region {
  pixels: Set<number>;
  bounds: { minX: number; minY: number; maxX: number; maxY: number };
}

export class WandDrawer extends BaseDrawer<Konva.Group> {
  private _wandConfig: any;

  // 魔杖工具不支持Transformer
  get needResize() {
    return false;
  }

  // 图像数据
  private imageData: ImageData | null = null;
  private canvasWidth = 0;
  private canvasHeight = 0;

  // 基础配置参数 - 会根据缩放级别动态调整，现在可以通过构造函数传入
  private baseBufferSize: number;
  private baseSensitivity: number;
  private mode: string;

  // 状态管理 - 优化参数以提高绘制质量和连续性
  private isDragging = false;
  private lastPoint: { x: number; y: number } | null = null;
  private dragStartTime = 0;

  // 数据存储
  private regionShapes: Konva.Shape[] = [];
  private currentSelection: Set<number> = new Set();

  constructor(
    layer: Layer,
    viewer: OpenSeadragon.Viewer,
    wandConfig: any,
    options?: {
      baseBufferSize?: number;
      baseSensitivity?: number;
      mode?: string;
    }
  ) {
    super(layer, viewer, wandConfig);
    this._wandConfig = wandConfig;
    this.startPoint = {};

    // 设置可配置参数，提供默认值
    this.baseBufferSize = options?.baseBufferSize ?? 200; // 减小基础缓冲区大小，提高精度
    this.baseSensitivity = options?.baseSensitivity ?? 20; // 降低基础敏感度，减少过度选择
    this.mode = options?.mode ?? 'gray'; // 模式，替代wandType
  }

  /**
   * 根据当前缩放级别动态计算参数 - 修正逻辑
   */
  private getDynamicParams() {
    const zoom = this.viewer.viewport.getZoom();
    const zoomFactor = Math.max(0.3, Math.min(2.0, zoom)); // 限制缩放因子范围

    // 反向缩放因子：放大时变小，缩小时变大
    const inverseZoomFactor = 1 / zoomFactor;
    const scaledFactor = Math.max(0.5, Math.min(3.0, inverseZoomFactor));

    const params = {
      bufferSize: Math.round(this.baseBufferSize * scaledFactor),
      bufferCenter: Math.round((this.baseBufferSize * scaledFactor) / 2),
      sensitivity: Math.round(this.baseSensitivity * (0.9 + scaledFactor * 0.2)), // 更保守的敏感度调整
      maxRegionPixels: Math.round(2000 * scaledFactor * scaledFactor), // 减小最大区域像素数
    };

    return params;
  }

  override start() {
    super.start();
    this.viewer.setMouseNavEnabled(false);
    this.layer.getStage().container().style.cursor = 'crosshair';
    const canvasDiv = this.layer.getStage().container();
    if (canvasDiv) {
      canvasDiv.style.zIndex = '2';
    }
  }

  override end() {
    this.isDragging = false;
    this.viewer.setMouseNavEnabled(true);
    this.layer.getStage().container().style.cursor = 'default';
    const canvasDiv = this.layer.getStage().container();
    if (canvasDiv) {
      canvasDiv.style.zIndex = '1';
    }

    // 清理所有状态
    this.clearAllState();

    super.end();
  }

  /**
   * 重置状态以便继续选择，但保持工具激活
   */
  private resetForNextSelection() {
    // 重置绘制状态
    this.isDragging = false;
    this.lastPoint = null;
    this.dragStartTime = 0;
    this.currentSelection.clear();
    this.regionShapes = [];
    this.startPoint = {};

    // 保持工具状态为 end，这样可以继续使用
    this.status = DrawStatus.end;
  }

  /**
   * 清理所有状态（完全结束工具时使用）
   */
  private clearAllState() {
    // 清除所有形状
    this.regionShapes.forEach((shape) => shape.destroy());
    this.regionShapes = [];
    this.currentSelection.clear();

    // 重置所有状态
    this.isDragging = false;
    this.lastPoint = null;
    this.dragStartTime = 0;
    this.imageData = null;
    this.node = null;
    this.startPoint = {};

    this.layer.draw();
  }

  override drawDown() {
    // 每次点击都开始新的选择，类似矩形工具的行为
    this.start();

    // 使用标准的坐标获取方式（与其他工具保持一致）
    this.startPoint = this.curPoint;
    this.isDragging = true;

    // 初始化 lastPoint 为像素坐标
    const pt = new OpenSeadragon.Point(
      Number(this.startPoint.imageX),
      Number(this.startPoint.imageY)
    );
    const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
    const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);
    this.lastPoint = { x: Math.round(pixelPoint.x), y: Math.round(pixelPoint.y) };

    this.dragStartTime = performance.now();

    // 重置处理状态，确保新的绘制会话是干净的
    this.currentSelection.clear();

    // 清除之前的区域形状（这些是临时的拖动轮廓）
    this.regionShapes.forEach((shape) => shape.destroy());
    this.regionShapes = [];

    // 总是创建新节点，用于当前的选择会话
    this.node = this.createNode();
    this.layer.add(this.node);
    this.setNodeId();

    // 异步获取图像数据并执行初始选择
    this.initializeWandSelection();
  }

  /**
   * 初始化魔杖选择 - 基于wand-tool.js的实现，支持一次性检测多个连接区域
   */
  private async initializeWandSelection() {
    try {
      // 获取图像数据
      await this.getImageDataFromViewer();

      // 清除之前的选择
      this.clearSelection();

      // 执行初始选择 - 使用连续选择方式
      const hasInitialRegions = await this.performContinuousWandSelection(
        this.startPoint.imageX!,
        this.startPoint.imageY!
      );

      // 如果有初始选择，生成初始轮廓
      if (hasInitialRegions) {
        this.updateContinuousContour();
      }

      // 初始选择完成
    } catch (error) {
      console.error('魔杖初始化失败:', error);
    }
  }

  override drawMove() {
    if (!this.isDragging || !this.imageData) return;

    const currentPoint = this.curPoint;
    const currentTime = Date.now();

    // 频率限制 - 但仍然更新lastPoint以保持连续性
    if (currentTime - this.dragStartTime < 16) {
      // 约60fps
      // 即使跳过处理，也要更新lastPoint以保持轨迹连续性
      this.lastPoint = { x: currentPoint.x, y: currentPoint.y };
      return;
    }

    this.dragStartTime = currentTime;

    // 使用智能边缘跟随系统
    this.performContinuousWandSelection(currentPoint.imageX!, currentPoint.imageY!)
      .then((success) => {
        if (success) {
          // 更新轮廓显示
          this.updateContinuousContour();
        }
      })
      .catch((error) => {
        console.error('边缘跟随失败:', error);
      });

    // 更新最后位置 - 这对边缘跟随至关重要，存储像素坐标
    const pt = new OpenSeadragon.Point(Number(currentPoint.imageX), Number(currentPoint.imageY));
    const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
    const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);
    this.lastPoint = { x: Math.round(pixelPoint.x), y: Math.round(pixelPoint.y) };
  }

  /**
   * 执行连续魔棒选择 - 智能边缘跟随版本
   */
  private async performContinuousWandSelection(imageX: number, imageY: number): Promise<boolean> {
    if (!this.imageData) return false;

    // 使用正确的坐标转换方式，与其他工具保持一致
    let x: number, y: number;

    if (imageX !== undefined && imageY !== undefined) {
      // 如果传入的是图像坐标，转换为像素坐标
      const pt = new OpenSeadragon.Point(Number(imageX), Number(imageY));
      const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
      const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);
      x = Math.round(pixelPoint.x);
      y = Math.round(pixelPoint.y);
    } else {
      // 使用当前鼠标位置
      const currentPoint = this.curPoint;
      const pt = new OpenSeadragon.Point(Number(currentPoint.imageX), Number(currentPoint.imageY));
      const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
      const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);
      x = Math.round(pixelPoint.x);
      y = Math.round(pixelPoint.y);
    }

    // 如果有现有选择，使用智能边缘跟随
    if (this.currentSelection.size > 0) {
      const edgeFollowResult = await this.performEdgeFollowing(x, y);

      // 如果边缘跟随失败，回退到常规区域检测
      if (!edgeFollowResult) {
        const fallbackRegion = await this.detectRegion(x, y);
        if (fallbackRegion && fallbackRegion.pixels.size > 0) {
          return this.applyRegionOperation(fallbackRegion);
        }
      }

      return edgeFollowResult;
    }

    // 首次选择时的常规检测
    const newRegion = await this.detectRegion(x, y);
    if (!newRegion || newRegion.pixels.size === 0) {
      return false;
    }

    // 应用区域操作
    return this.applyRegionOperation(newRegion);
  }

  /**
   * 应用区域操作 - 添加到当前选择
   */
  private applyRegionOperation(region: any): boolean {
    let operationCount = 0;

    // 添加到当前选择
    region.pixels.forEach((pixel: number) => {
      if (!this.currentSelection.has(pixel)) {
        this.currentSelection.add(pixel);
        operationCount++;
      }
    });

    return operationCount > 0;
  }

  /**
   * 智能边缘跟随 - 始终跟随鼠标方向扩展边缘
   */
  private async performEdgeFollowing(mouseX: number, mouseY: number): Promise<boolean> {
    // 转换图像坐标为像素坐标
    const pt = new OpenSeadragon.Point(Number(mouseX), Number(mouseY));
    const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
    const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);
    const pixelX = Math.round(pixelPoint.x);
    const pixelY = Math.round(pixelPoint.y);

    if (!this.lastPoint || this.currentSelection.size === 0) {
      return false;
    }

    // 计算鼠标移动方向（使用像素坐标）
    const deltaX = pixelX - this.lastPoint.x;
    const deltaY = pixelY - this.lastPoint.y;
    const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    if (moveDistance < 1) {
      // 降低移动距离要求
      return false;
    }

    // 标准化方向向量
    const dirX = deltaX / moveDistance;
    const dirY = deltaY / moveDistance;

    // 在移动方向上寻找最佳扩展点（使用像素坐标）
    const expansionPoints = this.findExpansionPointsInDirection(pixelX, pixelY, dirX, dirY);

    let totalOperations = 0;
    for (const point of expansionPoints) {
      const operations = await this.expandAtPoint(point.x, point.y);
      totalOperations += operations;
    }

    if (totalOperations > 0) {
      return true;
    }

    // 如果直接扩展失败，尝试跳跃式边缘检测（使用像素坐标）
    return await this.performJumpEdgeDetection(pixelX, pixelY, dirX, dirY);
  }

  /**
   * 在指定方向上寻找扩展点 - 限制在鼠标周围的缓冲区域内
   */
  private findExpansionPointsInDirection(
    mouseX: number,
    mouseY: number,
    dirX: number,
    dirY: number
  ): Array<{ x: number; y: number }> {
    const points: Array<{ x: number; y: number }> = [];
    const bufferRadius = 15; // 减小缓冲区域半径，提高精度

    // 获取鼠标周围缓冲区域内的边界点
    const nearbyBoundaryPoints = this.getBoundaryPointsInBuffer(mouseX, mouseY, bufferRadius);

    if (nearbyBoundaryPoints.length === 0) {
      return points;
    }

    // 在鼠标移动方向上寻找最佳边界点
    let bestPoints: Array<{ x: number; y: number; score: number }> = [];

    for (const boundaryPoint of nearbyBoundaryPoints) {
      // 计算边界点到鼠标的向量
      const toMouseX = mouseX - boundaryPoint.x;
      const toMouseY = mouseY - boundaryPoint.y;
      const distance = Math.sqrt(toMouseX * toMouseX + toMouseY * toMouseY);

      if (distance === 0) continue;

      // 计算方向一致性（点积）
      const normalizedToMouseX = toMouseX / distance;
      const normalizedToMouseY = toMouseY / distance;
      const directionScore = dirX * normalizedToMouseX + dirY * normalizedToMouseY;

      // 提高方向要求（点积 > 0.5，更严格的方向要求）
      if (directionScore > 0.5) {
        bestPoints.push({
          x: boundaryPoint.x,
          y: boundaryPoint.y,
          score: directionScore / (1 + distance * 0.02), // 增加距离权重
        });
      }
    }

    // 按分数排序，取较少的点以提高精度
    bestPoints.sort((a, b) => b.score - a.score);
    const topPoints = bestPoints.slice(0, Math.min(3, bestPoints.length));

    // 在每个最佳边界点周围生成扩展候选点
    for (const point of topPoints) {
      // 在移动方向上生成候选点，减小步长
      for (let step = 1; step <= 2; step++) {
        const candidateX = Math.round(point.x + dirX * step * 3);
        const candidateY = Math.round(point.y + dirY * step * 3);

        if (this.isValidCoordinate(candidateX, candidateY)) {
          points.push({ x: candidateX, y: candidateY });
        }
      }
    }

    return points;
  }

  /**
   * 获取鼠标周围缓冲区域内的边界点
   */
  private getBoundaryPointsInBuffer(
    mouseX: number,
    mouseY: number,
    bufferRadius: number
  ): Array<{ x: number; y: number }> {
    const boundaryPoints: Array<{ x: number; y: number }> = [];

    if (this.currentSelection.size === 0) {
      return boundaryPoints;
    }

    // 计算缓冲区域的边界
    const minX = Math.max(0, Math.floor(mouseX - bufferRadius));
    const maxX = Math.min(this.canvasWidth - 1, Math.ceil(mouseX + bufferRadius));
    const minY = Math.max(0, Math.floor(mouseY - bufferRadius));
    const maxY = Math.min(this.canvasHeight - 1, Math.ceil(mouseY + bufferRadius));

    // 只检查缓冲区域内的像素
    for (let y = minY; y <= maxY; y++) {
      for (let x = minX; x <= maxX; x++) {
        const pixelIndex = y * this.canvasWidth + x;

        // 检查是否在当前选择中
        if (!this.currentSelection.has(pixelIndex)) {
          continue;
        }

        // 检查是否在缓冲区域内
        const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);
        if (distanceToMouse > bufferRadius) {
          continue;
        }

        // 检查是否为边界点（至少有一个相邻像素不在选择中）
        let isBoundary = false;
        const neighbors = [
          [-1, -1],
          [-1, 0],
          [-1, 1],
          [0, -1],
          [0, 1],
          [1, -1],
          [1, 0],
          [1, 1],
        ];

        for (const [dx, dy] of neighbors) {
          const nx = x + dx;
          const ny = y + dy;

          if (nx < 0 || nx >= this.canvasWidth || ny < 0 || ny >= this.canvasHeight) {
            isBoundary = true;
            break;
          }

          const neighborIndex = ny * this.canvasWidth + nx;
          if (!this.currentSelection.has(neighborIndex)) {
            isBoundary = true;
            break;
          }
        }

        if (isBoundary) {
          boundaryPoints.push({ x, y });
        }
      }
    }

    return boundaryPoints;
  }

  /**
   * 检查坐标是否有效
   */
  private isValidCoordinate(x: number, y: number): boolean {
    return x >= 0 && x < this.canvasWidth && y >= 0 && y < this.canvasHeight;
  }

  /**
   * 在指定点进行扩展
   */
  private async expandAtPoint(x: number, y: number): Promise<number> {
    if (!this.isValidCoordinate(x, y)) {
      return 0;
    }

    // 检测该点的区域
    const region = await this.detectRegion(x, y);
    if (!region || region.pixels.size === 0) {
      return 0;
    }

    // 应用区域操作
    return this.applyRegionOperation(region) ? region.pixels.size : 0;
  }

  /**
   * 跳跃式边缘检测 - 限制在缓冲区域内，当常规扩展失败时使用
   */
  private async performJumpEdgeDetection(
    mouseX: number,
    mouseY: number,
    dirX: number,
    dirY: number
  ): Promise<boolean> {
    const bufferRadius = 15; // 进一步减小跳跃检测的缓冲区域

    // 在鼠标移动方向上进行更保守的步长搜索
    const searchSteps = [2, 4, 6, 8];

    for (const step of searchSteps) {
      const searchX = Math.round(mouseX + dirX * step);
      const searchY = Math.round(mouseY + dirY * step);

      if (!this.isValidCoordinate(searchX, searchY)) {
        continue;
      }

      // 检查搜索点是否在缓冲区域内
      const distanceToMouse = Math.sqrt((searchX - mouseX) ** 2 + (searchY - mouseY) ** 2);
      if (distanceToMouse > bufferRadius) {
        continue;
      }

      // 尝试在搜索点进行区域检测
      const region = await this.detectRegion(searchX, searchY);
      if (region && region.pixels.size > 0) {
        // 检查这个区域是否与现有选择相邻或重叠
        const isConnected = this.isRegionConnectedToSelection(region);

        if (isConnected) {
          // 应用区域操作
          const success = this.applyRegionOperation(region);

          if (success) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * 检查区域是否与现有选择连接
   */
  private isRegionConnectedToSelection(region: any): boolean {
    if (this.currentSelection.size === 0) {
      return true; // 如果没有现有选择，任何区域都可以连接
    }

    // 检查区域中的任何像素是否与现有选择相邻
    for (const pixelIndex of region.pixels) {
      const x = pixelIndex % this.canvasWidth;
      const y = Math.floor(pixelIndex / this.canvasWidth);

      // 检查8个相邻位置
      const neighbors = [
        [-1, -1],
        [-1, 0],
        [-1, 1],
        [0, -1],
        [0, 1],
        [1, -1],
        [1, 0],
        [1, 1],
      ];

      for (const [dx, dy] of neighbors) {
        const nx = x + dx;
        const ny = y + dy;

        if (this.isValidCoordinate(nx, ny)) {
          const neighborIndex = ny * this.canvasWidth + nx;
          if (this.currentSelection.has(neighborIndex)) {
            return true; // 找到连接点
          }
        }
      }
    }

    return false;
  }

  /**
   * 更新连续轮廓 - 防抖动优化版本
   */
  private updateContinuousContour() {
    if (this.currentSelection.size === 0) return;

    // 简化：移除防抖动逻辑，直接更新轮廓

    // 确保节点存在
    if (!this.node) {
      console.warn('⚠️ 节点不存在，无法更新轮廓');
      return;
    }

    // 计算边界
    const width = this.canvasWidth;
    let minX = Infinity,
      maxX = -Infinity;
    let minY = Infinity,
      maxY = -Infinity;

    this.currentSelection.forEach((pixelIndex) => {
      const x = pixelIndex % width;
      const y = Math.floor(pixelIndex / width);
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    });

    // 生成轮廓
    const contour = this.extractOuterContour(this.currentSelection, { minX, minY, maxX, maxY });

    if (contour.length < 6) return;

    // 只清除当前节点的子元素，不销毁regionShapes数组中的形状
    // 因为这些形状可能已经被添加到其他地方
    this.node.removeChildren();

    // 创建新形状 - 魔杖工具不支持变换
    const shape = new Konva.Line({
      points: contour,
      stroke: '#edea16',
      strokeWidth: 3,
      strokeScaleEnabled: false,
      closed: true,
      lineJoin: 'round',
      lineCap: 'round',
      // 禁用变换功能
      draggable: false,
      transformsEnabled: 'none',
      listening: false,
    });

    // 添加新形状到节点
    this.node.add(shape);

    // 更新regionShapes数组，只保留当前形状
    this.regionShapes.forEach((oldShape) => oldShape.destroy());
    this.regionShapes = [shape];

    this.layer.draw();
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._wandConfig.ev)) {
      this.isDragging = false;
      this.status = DrawStatus.drawingCompleted;

      // 创建标记并调用回调，但不结束工具
      this.createMarkerAndCallback();

      // 重置状态以便继续使用，而不是调用 end()
      this.resetForNextSelection();
    }
  }

  // 创建节点（魔棒工具创建的是一个组，包含多个区域形状）
  // 魔杖工具不支持Transformer，禁用所有变换功能
  createNode() {
    const group = new Konva.Group({
      name: DrawType.wand,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
      // 禁用所有变换功能
      draggable: false,
      transformsEnabled: 'none',
      // 禁用事件处理，防止意外的交互
      listening: false,
    });

    // 确保组内的所有子元素也不支持变换
    group.on('add', (e) => {
      const child = e.child;
      if (child) {
        child.setAttrs({
          draggable: false,
          transformsEnabled: 'none',
          listening: false,
        });
      }
    });

    return group;
  }

  /**
   * 从viewer获取图像数据
   */
  private async getImageDataFromViewer(): Promise<ImageData> {
    const canvas = this.viewer.drawer.canvas as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Cannot get 2D context');

    this.canvasWidth = canvas.width;
    this.canvasHeight = canvas.height;
    this.imageData = ctx.getImageData(0, 0, this.canvasWidth, this.canvasHeight);

    return this.imageData;
  }

  /**
   * 检测独立区域 - 使用缓冲区配置
   */
  private async detectRegion(startX: number, startY: number): Promise<Region | null> {
    if (!this.imageData) return null;

    const width = this.canvasWidth;
    const height = this.canvasHeight;
    const data = this.imageData.data;

    // 确保坐标在范围内，并增加边缘稳定性
    const margin = 3; // 增加边距，避免边缘不稳定
    if (
      startX < margin ||
      startX >= width - margin ||
      startY < margin ||
      startY >= height - margin
    ) {
      return null;
    }

    startX = Math.max(margin, Math.min(startX, width - 1 - margin));
    startY = Math.max(margin, Math.min(startY, height - 1 - margin));

    // 获取动态参数
    const { bufferCenter, bufferSize } = this.getDynamicParams();

    // 使用更小的搜索边界，提高精度
    const searchRadius = Math.min(bufferCenter * 0.7, 80); // 减小搜索半径
    const searchMinX = Math.max(0, startX - searchRadius);
    const searchMaxX = Math.min(width - 1, startX + searchRadius);
    const searchMinY = Math.max(0, startY - searchRadius);
    const searchMaxY = Math.min(height - 1, startY + searchRadius);

    // 获取起始点的颜色值
    const startIndex = (startY * width + startX) * 4;
    const startColor = this.getPixelValue(data, startIndex);

    // 使用洪水填充算法检测连通区域（带约束）
    const visited = new Uint8Array(width * height);
    const queue = [{ x: startX, y: startY }];
    const regionPixels = new Set<number>();

    let minX = startX,
      maxX = startX;
    let minY = startY,
      maxY = startY;

    // 使用缓冲区大小作为最大区域限制
    const maxBufferPixels = bufferSize * bufferSize;

    while (queue.length > 0 && regionPixels.size < maxBufferPixels) {
      const { x, y } = queue.shift()!;
      const index = y * width + x;

      if (visited[index]) continue;
      if (x < 0 || x >= width || y < 0 || y >= height) continue;

      // 限制搜索范围：只在缓冲区内搜索
      if (x < searchMinX || x > searchMaxX || y < searchMinY || y > searchMaxY) {
        continue;
      }

      visited[index] = 1;

      // 获取当前像素的颜色值
      const currentIndex = (y * width + x) * 4;
      const currentColor = this.getPixelValue(data, currentIndex);

      // 检查颜色相似性
      if (this.isColorSimilar(startColor, currentColor)) {
        regionPixels.add(index);

        // 更新边界
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);

        // 添加8邻域像素到队列（只有在缓冲区大小未超限时）
        if (regionPixels.size < maxBufferPixels) {
          const neighbors = [
            { x: x + 1, y: y },
            { x: x - 1, y: y },
            { x: x, y: y + 1 },
            { x: x, y: y - 1 },
            { x: x + 1, y: y + 1 },
            { x: x - 1, y: y + 1 },
            { x: x + 1, y: y - 1 },
            { x: x - 1, y: y - 1 },
          ];

          neighbors.forEach((neighbor) => {
            const neighborIndex = neighbor.y * width + neighbor.x;
            if (
              neighbor.x >= 0 &&
              neighbor.x < width &&
              neighbor.y >= 0 &&
              neighbor.y < height &&
              !visited[neighborIndex] &&
              // 确保邻居也在搜索范围内
              neighbor.x >= searchMinX &&
              neighbor.x <= searchMaxX &&
              neighbor.y >= searchMinY &&
              neighbor.y <= searchMaxY
            ) {
              queue.push(neighbor);
            }
          });
        }
      }
    }

    if (regionPixels.size === 0) return null;

    // 更保守的区域扩展策略 - 只在小区域时进行适度扩展
    const { maxRegionPixels } = this.getDynamicParams();
    if (regionPixels.size < maxRegionPixels * 0.5) {
      // 只对较小的区域进行扩展
      const expandedPixels = this.expandRegion(regionPixels, data, width, height, startColor);
      if (expandedPixels.size > regionPixels.size * 1.3) {
        // 提高扩展要求，需要30%以上的增长才接受
        // 重新计算边界
        let newMinX = width,
          newMaxX = 0,
          newMinY = height,
          newMaxY = 0;
        for (const pixelIndex of expandedPixels) {
          const x = pixelIndex % width;
          const y = Math.floor(pixelIndex / width);
          newMinX = Math.min(newMinX, x);
          newMaxX = Math.max(newMaxX, x);
          newMinY = Math.min(newMinY, y);
          newMaxY = Math.max(newMaxY, y);
        }
        minX = newMinX;
        maxX = newMaxX;
        minY = newMinY;
        maxY = newMaxY;
        regionPixels.clear();
        expandedPixels.forEach((p: number) => regionPixels.add(p));
      }
    }

    return {
      pixels: regionPixels,
      bounds: { minX, minY, maxX, maxY },
    };
  }

  /**
   * 扩展区域 - 改进的边缘扩展算法，更积极地向外扩展
   */
  private expandRegion(
    originalPixels: Set<number>,
    data: Uint8ClampedArray,
    width: number,
    height: number,
    startColor: any
  ): Set<number> {
    const expandedPixels = new Set(originalPixels);
    const visited = new Uint8Array(width * height);

    // 标记原始像素为已访问
    for (const pixelIndex of originalPixels) {
      visited[pixelIndex] = 1;
    }

    // 使用更保守的扩展策略，避免过度选择
    const { sensitivity } = this.getDynamicParams();
    const baseExpandedSensitivity = sensitivity * 1.1; // 更保守的基础阈值

    // 减少扩展轮数，避免过度扩展
    for (let round = 0; round < 2; round++) {
      const currentBoundary = new Set<number>();

      // 每轮使用递减的敏感度，保持相对保守
      const roundSensitivity = baseExpandedSensitivity * (1 + (1 - round) * 0.1);

      // 找到当前区域的边界像素
      for (const pixelIndex of expandedPixels) {
        const x = pixelIndex % width;
        const y = Math.floor(pixelIndex / width);

        // 检查8邻域
        const neighbors = [
          { x: x + 1, y: y },
          { x: x - 1, y: y },
          { x: x, y: y + 1 },
          { x: x, y: y - 1 },
          { x: x + 1, y: y + 1 },
          { x: x - 1, y: y + 1 },
          { x: x + 1, y: y - 1 },
          { x: x - 1, y: y - 1 },
        ];

        for (const neighbor of neighbors) {
          if (neighbor.x >= 0 && neighbor.x < width && neighbor.y >= 0 && neighbor.y < height) {
            const neighborIndex = neighbor.y * width + neighbor.x;
            if (!visited[neighborIndex]) {
              currentBoundary.add(neighborIndex);
            }
          }
        }
      }

      let addedInThisRound = 0;

      // 检查边界像素是否应该加入区域
      for (const boundaryIndex of currentBoundary) {
        const currentIndex = boundaryIndex * 4;
        const currentColor = this.getPixelValue(data, currentIndex);
        const distance = this.colorDistance(startColor, currentColor);

        if (distance <= roundSensitivity) {
          expandedPixels.add(boundaryIndex);
          visited[boundaryIndex] = 1;
          addedInThisRound++;
        }
      }

      // 如果这一轮没有添加任何像素，提前结束
      if (addedInThisRound === 0) {
        break;
      }
    }

    return expandedPixels;
  }

  /**
   * 获取像素值 - 基于缓冲区配置的模式
   */
  private getPixelValue(data: Uint8ClampedArray, index: number): any {
    const r = data[index];
    const g = data[index + 1];
    const b = data[index + 2];

    switch (this.mode) {
      case 'gray':
        return 0.299 * r + 0.587 * g + 0.114 * b;
      case 'rgb':
        return { r, g, b };
      default:
        return 0.299 * r + 0.587 * g + 0.114 * b;
    }
  }

  /**
   * 检查颜色相似性 - 基于动态敏感度
   */
  private isColorSimilar(color1: any, color2: any): boolean {
    const { sensitivity } = this.getDynamicParams();
    const distance = this.colorDistance(color1, color2);
    return distance <= sensitivity;
  }

  /**
   * 计算颜色距离 - 使用更严格的颜色匹配算法
   */
  private colorDistance(color1: any, color2: any): number {
    switch (this.mode) {
      case 'gray':
        return Math.abs(color1 - color2);
      case 'rgb':
        // 使用加权欧几里得距离，对绿色更敏感（人眼特性）
        const rDiff = color1.r - color2.r;
        const gDiff = color1.g - color2.g;
        const bDiff = color1.b - color2.b;
        return Math.sqrt(0.3 * rDiff * rDiff + 0.59 * gDiff * gDiff + 0.11 * bDiff * bDiff);
      default:
        return Math.abs(color1 - color2);
    }
  }

  /**
   * 简化轮廓点 - 改进的简化算法，确保保留足够的点
   */
  private simplifyContourPoints(
    points: Array<[number, number]>,
    tolerance: number
  ): Array<[number, number]> {
    if (points.length <= 6) return points;

    const simplified: Array<[number, number]> = [points[0]];
    let lastAddedIndex = 0;

    for (let i = 1; i < points.length - 1; i++) {
      const curr = points[i];
      const lastAdded = points[lastAddedIndex];
      const next = points[i + 1];

      // 计算当前点到上一个添加点和下一个点连线的距离
      const distance = this.pointToLineDistance(curr, lastAdded, next);

      // 如果距离大于容差，或者距离上次添加的点太远，保留这个点
      const distanceFromLast = Math.sqrt(
        Math.pow(curr[0] - lastAdded[0], 2) + Math.pow(curr[1] - lastAdded[1], 2)
      );

      if (distance > tolerance || distanceFromLast > 8) {
        // 增大最小距离，减少点数
        simplified.push(curr);
        lastAddedIndex = i;
      }
    }

    simplified.push(points[points.length - 1]);

    // 确保至少有6个点
    if (simplified.length < 6) {
      // 如果简化后点数不够，均匀采样原始点
      const step = Math.floor(points.length / 8);
      const sampled: Array<[number, number]> = [];
      for (let i = 0; i < points.length; i += step) {
        sampled.push(points[i]);
      }
      if (sampled.length >= 6) {
        return sampled;
      } else {
        return points; // 返回所有原始点
      }
    }

    return simplified;
  }

  /**
   * 计算点到直线的距离
   */
  private pointToLineDistance(
    point: [number, number],
    lineStart: [number, number],
    lineEnd: [number, number]
  ): number {
    const [x, y] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = y2 - y1;
    const B = x1 - x2;
    const C = x2 * y1 - x1 * y2;

    return Math.abs(A * x + B * y + C) / Math.sqrt(A * A + B * B);
  }

  /**
   * 清除选择 - 基于wand-tool.js的实现
   */
  private clearSelection() {
    // 清除所有形状
    this.regionShapes.forEach((shape) => shape.destroy());

    this.regionShapes = [];
    this.currentSelection.clear();
    this.lastPoint = null;

    this.layer.draw();
  }

  /**
   * 提取外轮廓 - 改进的边界对准算法，确保边缘线准确对准边界
   */
  private extractOuterContour(
    pixels: Set<number>,
    bounds: { minX: number; minY: number; maxX: number; maxY: number }
  ): number[] {
    const width = this.canvasWidth;

    // 创建像素网格
    const pixelGrid = new Map<string, boolean>();
    pixels.forEach((idx) => {
      const x = idx % width;
      const y = Math.floor(idx / width);
      pixelGrid.set(`${x},${y}`, true);
    });

    // 扩展边界以确保完整覆盖
    const expansion = 2;
    const alignedBounds = {
      minX: Math.max(0, bounds.minX - expansion),
      minY: Math.max(0, bounds.minY - expansion),
      maxX: Math.min(this.canvasWidth - 1, bounds.maxX + expansion),
      maxY: Math.min(this.canvasHeight - 1, bounds.maxY + expansion),
    };

    // 寻找最外层的起始点（最左上角的边缘点）
    let startX = -1,
      startY = -1;

    // 从左到右，从上到下寻找第一个边缘点
    outerLoop: for (let y = alignedBounds.minY; y <= alignedBounds.maxY; y++) {
      for (let x = alignedBounds.minX; x <= alignedBounds.maxX; x++) {
        if (pixelGrid.has(`${x},${y}`)) {
          // 检查是否是外边缘点
          const isOuterEdge = this.isOuterEdgePoint(x, y, pixelGrid);
          if (isOuterEdge) {
            startX = x;
            startY = y;
            break outerLoop;
          }
        }
      }
    }

    if (startX === -1) {
      // 没找到边缘点，使用对齐后的边界框
      const topLeft = this._konvaPointFromPixl(alignedBounds.minX, alignedBounds.minY);
      const topRight = this._konvaPointFromPixl(alignedBounds.maxX, alignedBounds.minY);
      const bottomRight = this._konvaPointFromPixl(alignedBounds.maxX, alignedBounds.maxY);
      const bottomLeft = this._konvaPointFromPixl(alignedBounds.minX, alignedBounds.maxY);
      return [
        topLeft.x,
        topLeft.y,
        topRight.x,
        topRight.y,
        bottomRight.x,
        bottomRight.y,
        bottomLeft.x,
        bottomLeft.y,
      ];
    }

    // 使用改进的轮廓追踪算法，确保边界对准
    const contourPoints = this.traceOuterContourWithAlignment(startX, startY, pixelGrid);

    // 简化轮廓点，但保持边界对准
    const simplified = this.simplifyContourPoints(contourPoints, 1.5); // 使用更小的容差保持精度

    // 转换为Konva坐标
    const konvaPoints: number[] = [];
    simplified.forEach(([x, y]) => {
      const konvaPoint = this._konvaPointFromPixl(x, y);
      konvaPoints.push(konvaPoint.x, konvaPoint.y);
    });

    return konvaPoints;
  }

  /**
   * 改进的轮廓追踪算法，确保边界对准
   */
  private traceOuterContourWithAlignment(
    startX: number,
    startY: number,
    pixelGrid: Map<string, boolean>
  ): Array<[number, number]> {
    const contourPoints: Array<[number, number]> = [];
    const directions = [
      [1, 0], // 右
      [1, 1], // 右下
      [0, 1], // 下
      [-1, 1], // 左下
      [-1, 0], // 左
      [-1, -1], // 左上
      [0, -1], // 上
      [1, -1], // 右上
    ];

    let currentX = startX;
    let currentY = startY;
    let direction = 0;
    const maxSteps = pixelGrid.size * 4;
    let steps = 0;

    do {
      // 添加当前点
      contourPoints.push([currentX, currentY]);

      // 寻找下一个外边缘点
      let found = false;
      for (let i = 0; i < 8; i++) {
        const checkDir = (direction + 6 + i) % 8; // 从左转90度开始检查
        const [dx, dy] = directions[checkDir];
        const nextX = currentX + dx;
        const nextY = currentY + dy;

        if (pixelGrid.has(`${nextX},${nextY}`)) {
          // 确保这是外边缘点
          const isOuterEdge = this.isOuterEdgePoint(nextX, nextY, pixelGrid);
          if (isOuterEdge) {
            currentX = nextX;
            currentY = nextY;
            direction = checkDir;
            found = true;
            break;
          }
        }
      }

      if (!found) break;
      steps++;
    } while ((currentX !== startX || currentY !== startY) && steps < maxSteps);

    return contourPoints;
  }

  /**
   * 检查是否是外边缘点
   */
  private isOuterEdgePoint(x: number, y: number, pixelGrid: Map<string, boolean>): boolean {
    // 检查8邻域，如果有任何一个邻居不在像素集合中，则是边缘点
    const neighbors = [
      [x - 1, y],
      [x + 1, y],
      [x, y - 1],
      [x, y + 1],
      [x - 1, y - 1],
      [x + 1, y - 1],
      [x - 1, y + 1],
      [x + 1, y + 1],
    ];

    return neighbors.some(([nx, ny]) => !pixelGrid.has(`${nx},${ny}`));
  }

  /**
   * 创建标记并调用回调
   */
  private createMarkerAndCallback() {
    if (this.regionShapes.length > 0 && this.node) {
      // 将所有区域形状添加到组中
      this.regionShapes.forEach((shape) => {
        this.node!.add(shape);
      });

      // 设置节点属性
      this.node.setAttrs({
        pixelCoords: {
          regionCount: this.regionShapes.length,
          selectionSize: this.currentSelection.size,
        },
        Description: `魔棒选择 - ${this.currentSelection.size} 个像素`,
      });

      this.layer.draw();

      // 触发绘制结束事件，通知上层组件
      this.emit('drawingEnd', {
        node: this.node,
      });
    }
  }
}
