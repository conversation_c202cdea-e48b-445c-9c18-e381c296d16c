# 魔杖工具 (WandDrawer) 参数配置使用指南

魔杖工具现在支持三个可配置参数，可以根据不同的使用场景进行调整。

## 可配置参数

### 1. `baseBufferSize` (基础缓冲区大小)
- **默认值**: 200
- **作用**: 控制魔杖选择的搜索范围大小
- **建议值**: 
  - 精细选择: 100-150
  - 标准选择: 200 (默认)
  - 大范围选择: 300-400

### 2. `baseSensitivity` (基础敏感度)
- **默认值**: 20
- **作用**: 控制颜色相似性的阈值，值越小越精确
- **建议值**:
  - 高精度选择: 10-15
  - 标准选择: 20 (默认)
  - 宽松选择: 30-40

### 3. `mode` (颜色模式)
- **默认值**: 'gray'
- **可选值**: 'gray' | 'rgb'
- **作用**: 
  - 'gray': 灰度模式，计算速度快
  - 'rgb': RGB模式，颜色识别更精确

## 使用方式

### 方式1: 通过 sqMarker.drawWand() 传递参数

```typescript
// 使用默认参数
sqMarker.drawWand();

// 自定义部分参数
sqMarker.drawWand({
  baseBufferSize: 150,
  baseSensitivity: 25
});

// 自定义所有参数
sqMarker.drawWand({
  baseBufferSize: 300,
  baseSensitivity: 15,
  mode: 'rgb',
  // 还可以包含其他样式参数
  stroke: '#ff0000',
  strokeWidth: 2
});
```

### 方式2: 在 Vue 组件中使用

```vue
<template>
  <div>
    <button @click="useHighPrecisionWand">高精度魔杖</button>
    <button @click="useStandardWand">标准魔杖</button>
    <button @click="useFastWand">快速魔杖</button>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const sqMarker = ref();

// 高精度选择 - 适合细节丰富的图像
const useHighPrecisionWand = () => {
  sqMarker.value?.drawWand({
    baseBufferSize: 120,
    baseSensitivity: 12,
    mode: 'rgb',
    stroke: '#00ff00',
    strokeWidth: 1
  });
};

// 标准选择 - 平衡性能和精度
const useStandardWand = () => {
  sqMarker.value?.drawWand({
    baseBufferSize: 200,
    baseSensitivity: 20,
    mode: 'gray'
  });
};

// 快速选择 - 适合大范围快速选择
const useFastWand = () => {
  sqMarker.value?.drawWand({
    baseBufferSize: 350,
    baseSensitivity: 35,
    mode: 'gray'
  });
};
</script>
```

### 方式3: 通过 setDrawingOption 预设参数

```typescript
// 预设魔杖参数
sqMarker.setDrawingOption({
  baseBufferSize: 180,
  baseSensitivity: 18,
  mode: 'rgb',
  stroke: '#0066cc',
  strokeWidth: 2
});

// 然后启动魔杖工具
sqMarker.drawWand();
```

## 参数调优建议

### 根据图像类型调整

1. **医学影像 (CT/MRI)**:
   ```typescript
   {
     baseBufferSize: 150,
     baseSensitivity: 15,
     mode: 'gray'
   }
   ```

2. **彩色照片**:
   ```typescript
   {
     baseBufferSize: 200,
     baseSensitivity: 25,
     mode: 'rgb'
   }
   ```

3. **线条图/示意图**:
   ```typescript
   {
     baseBufferSize: 100,
     baseSensitivity: 10,
     mode: 'gray'
   }
   ```

### 根据选择需求调整

1. **精细边缘选择**:
   - 减小 `baseBufferSize` (100-150)
   - 减小 `baseSensitivity` (10-15)
   - 使用 'rgb' 模式

2. **大区域快速选择**:
   - 增大 `baseBufferSize` (300-400)
   - 增大 `baseSensitivity` (30-40)
   - 使用 'gray' 模式

3. **平衡选择**:
   - 使用默认值或接近默认值
   - 根据实际效果微调

## 注意事项

1. **性能考虑**: 
   - `baseBufferSize` 越大，计算量越大
   - 'rgb' 模式比 'gray' 模式计算量更大

2. **精度权衡**:
   - 高精度设置可能导致选择过于碎片化
   - 低精度设置可能选择过多不相关区域

3. **动态调整**:
   - 参数会根据缩放级别自动调整
   - 放大时自动提高精度，缩小时自动降低精度

## 实际应用场景

```typescript
// 场景1: 医学影像中选择病灶区域
sqMarker.drawWand({
  baseBufferSize: 120,
  baseSensitivity: 12,
  mode: 'gray',
  stroke: '#ff0000',
  strokeWidth: 2
});

// 场景2: 照片中选择天空区域
sqMarker.drawWand({
  baseBufferSize: 300,
  baseSensitivity: 30,
  mode: 'rgb',
  stroke: '#0099ff',
  strokeWidth: 1
});

// 场景3: 技术图纸中选择特定部件
sqMarker.drawWand({
  baseBufferSize: 80,
  baseSensitivity: 8,
  mode: 'gray',
  stroke: '#00ff00',
  strokeWidth: 1
});
```
